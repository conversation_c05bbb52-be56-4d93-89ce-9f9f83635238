# This is a sample Python script.

# Press Shift+F10 to execute it or replace it with your code.
# Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.
import math

# import rpa as r
import datetime
import random
import sys

import telegram_reciever
from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
import tkinter as tk
import tagui_2 as r
import image_after_check
import signal
import shutil

import pytz
import traceback
from mongo.MongoDBHelper import MongoDBHelper
from my_tg_img_downloader import pyrogramMsgUtil
from my_tg_img_downloader import telegram_img_download

utc = pytz.UTC
IST = pytz.timezone('Asia/Shanghai')

root = tk.Tk()

SCREEN_WIDTH = root.winfo_screenwidth()
SCREEN_HEIGHT = root.winfo_screenheight()

file_path = '/Volumes/新加卷/download_list/download'
file_path_dir = '/Volumes/新加卷/download_list/*'
now = datetime.datetime.now().strftime('%Y-%m-%d')

yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = yesterday - datetime.timedelta(hours=8)
yesterday = utc.localize(yesterday)
his_set = set()

mongo = MongoDBHelper()

def cleanup_empty_folders_and_invalid_records():
    """
    清理任务：
    1. 遍历 /Volumes/新加卷/tg-image/YYYY-MM (当前月份) 的所有子文件夹，
       如果发现子文件夹里的图片都是0kb就删掉这个子文件夹
    2. 查询mongo数据库，如果发现保存的path路径不存在，就删掉对应的mongo数据库记录
    """
    print("开始执行清理任务...")

    # 任务1: 清理空图片文件夹
    current_month = datetime.datetime.now().strftime('%Y-%m')
    base_path = f'/Volumes/新加卷/tg-image/{current_month}'

    if os.path.exists(base_path):
        print(f"检查路径: {base_path}")

        # 遍历当前月份目录下的所有子文件夹
        for folder_name in os.listdir(base_path):
            folder_path = os.path.join(base_path, folder_name)

            if os.path.isdir(folder_path):
                print(f"检查文件夹: {folder_path}")

                # 检查文件夹中的所有图片文件
                image_files = []
                for file_name in os.listdir(folder_path):
                    file_path = os.path.join(folder_path, file_name)
                    if os.path.isfile(file_path):
                        # 检查是否为图片文件（根据扩展名）
                        if file_name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                            image_files.append(file_path)

                # 如果有图片文件，检查是否都是0kb
                if image_files:
                    all_empty = True
                    for img_file in image_files:
                        if os.path.getsize(img_file) > 0:
                            all_empty = False
                            break

                    # 如果所有图片都是0kb，删除整个文件夹
                    if all_empty:
                        print(f"删除空图片文件夹: {folder_path}")
                        try:
                            shutil.rmtree(folder_path)
                            print(f"成功删除文件夹: {folder_path}")
                        except Exception as e:
                            print(f"删除文件夹失败 {folder_path}: {e}")
                    else:
                        print(f"文件夹包含有效图片，保留: {folder_path}")
                else:
                    print(f"文件夹中没有图片文件: {folder_path}")
    else:
        print(f"当前月份目录不存在: {base_path}")

    # 任务2: 清理MongoDB中路径不存在的记录
    print("开始清理MongoDB中的无效记录...")

    try:
        mongo.connect()
        # 查询所有有path字段的记录
        records = list(mongo.find_data('tele_img', {"path": {"$ne": None, "$exists": True}}))

        deleted_count = 0
        for record in records:
            path = record.get('path')
            if path and not os.path.exists(path):
                print(f"删除无效路径记录: {path}")
                try:
                    mongo.delete_data('tele_img', {"_id": record["_id"]})
                    deleted_count += 1
                except Exception as e:
                    print(f"删除记录失败 {record['_id']}: {e}")

        print(f"共删除了 {deleted_count} 条无效路径记录")
        mongo.close()

    except Exception as e:
        print(f"清理MongoDB记录时出错: {e}")
        try:
            mongo.close()
        except:
            pass

    print("清理任务完成")

def load_his_urls():
    global his_set
    list_of_files = glob.glob(file_path_dir)
    latest_file = max(list_of_files, key=os.path.getctime)
    with open(latest_file, 'r') as log:
        lines = log.readlines()

    url_list = set(lines)
    url_list = set(map(lambda x: x.rstrip('\n'), url_list))
    his_set = url_list

def load_telegram_links(channel):
    r.url('https://web.telegram.org/k/#@' + channel)
    r.click('//*[@id="column-center"]/div/div/div[2]/div[1]/div[1]/div/div[1]/img')
    # r.click('/html/body/div[1]/div/div[2]/div/div/div[2]/div[1]/div[1]/div/div[1]/img')
    r.wait(2)
    # r.click('//*[@id="column-right"]/div/div/div[2]/div/div/div[3]/div[1]/div/nav/div[4]/div')
    r.click('//*[@id="column-right"]/div/div/div[2]/div/div/div[3]/div[1]/div/nav/div[5]/div')
    r.wait(5)

    content = r.read('page')
    soup = BeautifulSoup(content, features="html.parser")
    div = soup.find("div", {"id": "column-right"})
    links = div.findAll("a")
    links = list(filter(lambda x: x, links))
    links2 = list(filter(lambda x: 'href' in x.attrs and 'https://telegra.ph' in x.attrs['href'], links))

    links3 = list(map(lambda x: x.attrs['href'], links2))
    with open(file_path + now + ".log", 'a') as log:
        for link in links3:
            log.write(link)
            log.write("\n")
    links3 = list(filter(lambda x: x not in his_set, links3))
    print(links3)

    try:
        mongo.connect()
        links2 = list(filter(lambda x: x.attrs['href'] not in his_set, links2))
        for link in links2:
            data = mongo.count_data('tele_img', {"link": link.attrs['href']})
            if data == 0:
                mongo.insert_data('tele_img', {
                    "link": link.attrs['href'],
                    "title": link.next[19:],
                    "channel": channel,
                    "date": datetime.datetime.now().strftime('%Y-%m-%d')
                })
        mongo.close()
    except Exception as e:
        print(e)
    return links3

def download_from_link(content):
    r.run(r'/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args "chrome-extension://dbjbempljhcmhlfpfacalomonjpalpko/multiUrlExtractor.html" --start-maximized')

    r.clipboard(content)
    # r.wait(5)
    r.click(400, 550)
    r.keyboard('[cmd]v')
    r.click(300, 830)
    r.wait(20)
    r.keyboard('[ctrl]a')
    r.keyboard('[ctrl]d')
    r.wait(2)
    r.click(1218,418)
    r.wait(2)
    r.click(1400,420)



def main_func():
    load_his_urls()
    link_list = []
    link_list.extend(load_telegram_links('AnchorPic'))
    link_list.extend(load_telegram_links('douza23333'))
    link_list.extend(load_telegram_links('meitu520'))
    link_list.extend(load_telegram_links('miaotuya'))
    link_list.extend(load_telegram_links('MarioBase'))
    link_list.extend(load_telegram_links('TAOT808'))

    msg = ''
    for link in link_list:
        msg += link + '\n'

    print(msg)
    download_from_link(msg)
    r.telegram(-1001497463795, "I'va done today's work")


def run():
    # 在正式运行前执行清理任务
    cleanup_empty_folders_and_invalid_records()

    # v1
    # r.init(True)
    # main_func()
    # r.close()

    # v2
    # pyrogramMsgUtil.get_img_message_id('AnchorPic', 4998)
    pyrogramMsgUtil.get_img_message_id('douza23333', 52534)
    pyrogramMsgUtil.get_img_message_id('miaotuya', 39468)
    pyrogramMsgUtil.get_img_message_id('MarioBase', 35346)
    # pyrogramMsgUtil.get_img_message_id('TAOT808', 4998)
    telegram_img_download.startScrapy()


if __name__ == '__main__':
    run()





# See PyCharm help at https://www.jetbrains.com/help/pycharm/
